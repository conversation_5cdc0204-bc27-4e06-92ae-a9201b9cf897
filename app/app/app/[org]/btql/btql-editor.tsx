"use client";

import { useCallback, useMemo } from "react";
import TextEditor, { type TextEditorHandle } from "#/ui/text-editor";
import {
  btqlSupport,
  type SearchableItemInfo,
  type BtqlMode,
} from "#/utils/codemirror/btql-lang";
import { githubLightInit, githubDarkInit } from "@uiw/codemirror-theme-github";
import { useDarkMode } from "#/utils/useDarkMode";
import { sql } from "@codemirror/lang-sql";
import { type DataObjectType } from "#/utils/btapi/btapi";
import useEvent from "react-use-event-hook";

interface BtqlEditorProps {
  mode: BtqlMode;
  value: string;
  onValueChange?: (value: string) => void;
  onDebouncedSave?: (value: string) => void;
  onMetaEnter: () => void;
  dataSources?: {
    projects: SearchableItemInfo[];
    datasets: SearchableItemInfo[];
    experiments: SearchableItemInfo[];
    promptSessions: SearchableItemInfo[];
    orgs: SearchableItemInfo[];
  };
  className?: string;
  textEditorRef?: React.RefObject<TextEditorHandle | null>;
  placeholder?: string;
  autoFocus?: boolean;
  readOnly?: boolean;
  diffValue?: string;
  onFixWithLoop?: (message: string) => void;
}

const editorThemeSettings = {
  settings: { background: "transparent" },
};

export function BtqlEditor({
  value,
  onValueChange,
  onDebouncedSave,
  onMetaEnter,
  dataSources,
  className,
  textEditorRef,
  autoFocus,
  mode,
  placeholder = "Enter BTQL query",
  readOnly,
  diffValue,
  onFixWithLoop,
}: BtqlEditorProps) {
  const darkMode = useDarkMode();

  const searchItemsAsyncCallback = useEvent(
    async ({
      objectType,
      currentPrefix,
      projectScope,
    }: {
      objectType: DataObjectType;
      currentPrefix: string;
      projectScope?: string; // TODO: Remove
    }): Promise<SearchableItemInfo[]> => {
      const lowerPrefix = currentPrefix.toLowerCase();

      if (
        objectType === "project_logs" ||
        objectType === "project_prompts" ||
        objectType === "project_functions"
      ) {
        return (
          dataSources?.projects.filter(
            (proj) =>
              proj.name.toLowerCase().includes(lowerPrefix) ||
              proj.id.toLowerCase().includes(lowerPrefix),
          ) ?? []
        );
      } else if (objectType === "dataset") {
        return (
          dataSources?.datasets.filter(
            (dataset) =>
              dataset.name.toLowerCase().includes(lowerPrefix) ||
              dataset.id.toLowerCase().includes(lowerPrefix),
          ) ?? []
        );
      } else if (objectType === "experiment") {
        return (
          dataSources?.experiments.filter(
            (experiment) =>
              experiment.name.toLowerCase().includes(lowerPrefix) ||
              experiment.id.toLowerCase().includes(lowerPrefix),
          ) ?? []
        );
      } else if (
        objectType === "org_prompts" ||
        objectType === "org_functions"
      ) {
        return (
          dataSources?.orgs.filter(
            (org) =>
              org.name.toLowerCase().includes(lowerPrefix) ||
              org.id.toLowerCase().includes(lowerPrefix),
          ) ?? []
        );
      } else if (
        objectType === "prompt_session" ||
        objectType === "playground_logs"
      ) {
        return (
          dataSources?.promptSessions.filter(
            (ps) =>
              ps.name.toLowerCase().includes(lowerPrefix) ||
              ps.id.toLowerCase().includes(lowerPrefix),
          ) ?? []
        );
      } else {
        return [];
      }
    },
  );

  const extensions = useMemo(() => {
    return [
      darkMode
        ? githubDarkInit(editorThemeSettings)
        : githubLightInit(editorThemeSettings),
      sql(),
      btqlSupport({
        mode,
        searchItems: searchItemsAsyncCallback,
        onFixWithLoop,
      }),
    ];
  }, [darkMode, mode, searchItemsAsyncCallback, onFixWithLoop]);

  return (
    <>
      <TextEditor
        className={className}
        onSave={useCallback(
          (newValue: string | undefined) => {
            onDebouncedSave?.(newValue ?? "");
          },
          [onDebouncedSave],
        )}
        onChange={useCallback(
          (newValue: string | undefined) => {
            onValueChange?.(newValue ?? "");
          },
          [onValueChange],
        )}
        placeholder={placeholder}
        autoFocus={autoFocus}
        value={value}
        diffValue={diffValue}
        onMetaEnter={onMetaEnter}
        extensions={extensions}
        ref={textEditorRef}
        wrap
        readOnly={readOnly}
      />
    </>
  );
}
