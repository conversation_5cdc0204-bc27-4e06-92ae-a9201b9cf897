import { But<PERSON> } from "#/ui/button";
import {
  OptimizationChat,
  useIsLoopEnabled,
} from "#/ui/optimization/optimization-chat";
import {
  type BTQLContextObject,
  useGlobalChat,
} from "#/ui/optimization/use-global-chat-context";
import { type SearchableItemInfo } from "#/utils/codemirror/btql-lang";
import { useMemo } from "react";
import { CopyBtqlCodeSnippet } from "./btql-code-snippet";
import { BtqlRunButton } from "./btql-run-button";
import { formatBTQLQuery } from "./format-btql-query";

type Props = {
  runQuery: () => void;
  abortQuery: () => void;
  running: boolean;
  activeTabQuery: string;
  contextSelectorData: {
    dataSources: {
      projects: SearchableItemInfo[];
      datasets: SearchableItemInfo[];
      experiments: SearchableItemInfo[];
      promptSessions: SearchableItemInfo[];
      orgs: SearchableItemInfo[];
    };
    btqlTabs: BTQLContextObject[];
  };
  runSandboxQuery: (args: {
    query: string;
    title?: string;
    openNewTab?: boolean;
  }) => void;
};

export const BTQLHeaderButtons = ({
  runQuery,
  abortQuery,
  running,
  activeTabQuery,
  contextSelectorData,
  runSandboxQuery,
}: Props) => {
  const isLoopEnabled = useIsLoopEnabled();
  const { runBtqlConfirmationData, setRunBtqlConfirmationData } =
    useGlobalChat();
  const hasProposedEdits = useMemo(
    () =>
      runBtqlConfirmationData?.originalQuery ===
      formatBTQLQuery(activeTabQuery),
    [activeTabQuery, runBtqlConfirmationData?.originalQuery],
  );

  return (
    <div className="flex items-center justify-end gap-2 bg-primary-50 pl-1 pr-3">
      {hasProposedEdits ? (
        <>
          <Button
            variant="ghost"
            size="xs"
            className="text-primary-600"
            onClick={() => {
              runBtqlConfirmationData?.onCancel(undefined);
              setRunBtqlConfirmationData(null);
            }}
          >
            Skip
          </Button>
          <Button
            variant="primary"
            size="xs"
            onClick={() => {
              runBtqlConfirmationData?.onConfirm();
              setRunBtqlConfirmationData(null);
            }}
          >
            Accept
          </Button>
        </>
      ) : (
        <>
          <CopyBtqlCodeSnippet value={activeTabQuery} />
          <BtqlRunButton
            runQuery={runQuery}
            abortQuery={abortQuery}
            running={running}
            value={activeTabQuery}
          />
        </>
      )}
      {isLoopEnabled && (
        <OptimizationChat
          contextSelectorData={contextSelectorData}
          onRunInSandbox={runSandboxQuery}
        />
      )}
    </div>
  );
};
