:root {
  --fd-sidebar-width: 300px;
  --fd-toc-width: 180px;
}

#nd-sidebar {
  @apply flex-none;
  @apply bg-background;

  > div {
    @apply md:border-none;
  }

  .border-b {
    @apply border-b-0;
  }

  > div.px-4 {
    @apply px-5;
  }

  > div.border-t {
    @apply border-t-0 md:mx-4 py-2;
  }

  border-width: 0 !important;
  > div > div {
    border-width: 0 !important;
    > a {
      @apply h-12;
    }
  }

  a.font-medium {
    font-weight: normal !important;
  }

  div[data-radix-scroll-area-viewport] {
    a,
    button {
      @apply text-primary-700 hover:text-primary-900;
    }
  }

  .lucide-chevron-down,
  .lucide-external-link {
    @apply w-3 h-3 text-primary-600;
  }
}

.docs-content {
  > div > article {
    @apply w-full overflow-hidden md:pt-5;
  }

  article[class*="max-w-[860px]"] {
    @apply !max-w-[1200px];
  }

  article .prose li {
    list-style: none !important;
  }

  h2.bg-card > code {
    @apply bg-transparent flex-1 whitespace-pre-wrap p-0;
  }

  .font-mono {
    font-weight: normal !important;
  }

  /* toc styles */
  > .sticky {
    padding: 0;
    @apply flex-none;

    div[data-radix-scroll-area-viewport] {
      @apply py-12 pr-3;
      > div[data-radix-scroll-area-content] {
        @apply relative;
      }
    }
  }

  form.not-prose input {
    @apply text-xs border-primary-200;
  }
}

input[placeholder="Search"] {
  outline: none;
  box-shadow: none !important;
  border: 0;
}

[data-toc] h3,
#nd-toc h3 {
  display: none;
}
[data-toc] a[data-active],
#nd-toc a[data-active] {
  @apply text-xs font-normal;
}

.bg-fd-secondary {
  @apply bg-primary-100/60;
}

.bg-fd-card {
  @apply bg-primary-50/50;

  &[class*="hover:bg-fd-accent/80"] {
    @apply no-underline shadow-sm hover:bg-primary-100/80;

    p {
      @apply font-normal;
    }
  }
}

.bg-fd-popover {
  @apply bg-background;
}

[class*="group/accordion"] > [data-state][data-orientation="vertical"] > div {
  @apply flex flex-col gap-3;
}

.docs-content[class*="reference/api/"] article {
  @apply max-w-[1200px];
}

#nd-toc {
  div[class*="mt-[var(--fd-top)]"] {
    @apply translate-y-[-48px];
  }
}

#nd-page {
  @apply !max-w-none;

  .prose {
    @apply max-w-none;
  }

  div[class*="max-h-[600px]"] {
    @apply max-h-none;
  }

  /* Make Fumadocs API headings not overlap with prose headings on mobile. */
  > article div.sticky {
    position: initial;
  }
}

.docs-search-item {
  .docs-search-item-title em {
    @apply bg-orange-50 dark:bg-orange-950/80;
  }
  em {
    @apply text-orange-900 dark:text-orange-200 not-italic;
  }
}
