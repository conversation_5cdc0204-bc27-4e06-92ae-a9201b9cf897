import { DocsLayout } from "fumadocs-ui/layouts/docs";
import type { ReactNode } from "react";
import { Logo } from "#/ui/landing/logo";
import "./docs-style.css";
import { RootProvider as FumadocsProvider } from "fumadocs-ui/provider";
import { pageTree } from "./source";
import { TooltipProvider } from "#/ui/tooltip";
import { Toaster } from "#/ui/toaster";
import { GoogleAnalytics } from "#/ui/analytics-provder";
import { getNonce } from "#/security/csp";
import { ReactQueryProvider } from "#/ui/query-provider";
import DocsSearchDialog from "#/ui/docs/docs-search-dialog";

export default async function RootDocsLayout({
  children,
}: {
  children: ReactNode;
}) {
  return (
    <ReactQueryProvider>
      <FumadocsProvider
        search={{
          SearchDialog: DocsSearchDialog,
        }}
        theme={{
          defaultTheme: "system",
          enableSystem: true,
          attribute: "class",
          disableTransitionOnChange: true,
          nonce: await getNonce(),
        }}
      >
        <DocsLayout
          tree={pageTree}
          nav={{
            title: (
              <div className="sm:mb-2 sm:py-1.5 sm:pl-2">
                <Logo width={140} />
              </div>
            ),
            url: "/docs",
            transparentMode: "none",
          }}
          sidebar={{
            defaultOpenLevel: 0,
          }}
        >
          <TooltipProvider>
            {children}
            <Toaster />
          </TooltipProvider>
        </DocsLayout>
        <GoogleAnalytics />
      </FumadocsProvider>
    </ReactQueryProvider>
  );
}
