import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuTrigger,
} from "#/ui/dropdown-menu";
import { Search } from "lucide-react";
import { Input } from "#/ui/input";
import {
  type ForwardRefExoticComponent,
  type HTMLAttributes,
  type PropsWithChildren,
  type RefAttributes,
  useCallback,
  useLayoutEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { type DropdownMenuContentProps } from "@radix-ui/react-dropdown-menu";
import { useVirtualizer } from "@tanstack/react-virtual";
import { useDebounce } from "#/utils/useDebouncedCallback";
import { cn } from "#/utils/classnames";

export type AdditionalAction = {
  label: React.ReactNode;
  className?: string;
  disabled?: boolean;
  onSelect?: (e: Event) => void;
};

export interface NestedSubGroup<T> {
  groupLabel: string;
  items?: T[];
  subGroups?: NestedSubGroup<T>[];
}

interface SubGroupRendererProps<T> {
  subGroup: NestedSubGroup<T>;
  index: number;
  isFirstGroup: boolean;
  hasPrecedingItems: boolean;
  search: string;
  debouncedSearch: string;
  firstSubMenuRef: React.RefObject<HTMLDivElement | null>;
  lastItemRef: React.RefObject<HTMLDivElement | null>;
  inputRef: React.RefObject<HTMLInputElement | null>;
  renderGroupLabel?: (label: string, nested?: boolean) => React.ReactNode;
  renderItems?: (
    items: T[],
    DropdownItemComponent: ForwardRefExoticComponent<
      { item: T } & RefAttributes<HTMLDivElement>
    >,
    isSearching?: boolean,
  ) => React.ReactNode;
  DropdownItemComponent: ForwardRefExoticComponent<
    { item: T } & RefAttributes<HTMLDivElement> & HTMLAttributes<HTMLDivElement>
  >;
  pruneItems: (items: T[]) => T[];
}

const SubGroupRenderer = <T,>({
  subGroup,
  index,
  isFirstGroup,
  hasPrecedingItems,
  search,
  debouncedSearch,
  firstSubMenuRef,
  lastItemRef,
  inputRef,
  renderGroupLabel,
  renderItems,
  DropdownItemComponent,
  pruneItems,
}: SubGroupRendererProps<T>) => {
  const { groupLabel, items, subGroups: nestedSubGroups } = subGroup;
  const prunedItems = useMemo(
    () => pruneItems(items ?? []),
    [pruneItems, items],
  );
  const hasNestedGroups = nestedSubGroups && nestedSubGroups.length > 0;
  const virtualizedItems = prunedItems ?? [];

  const scrollContainerRef = useRef<HTMLDivElement>(null);

  const virtualizer = useVirtualizer({
    count: virtualizedItems.length,
    getScrollElement: () => scrollContainerRef.current,
    estimateSize: () => 28,
    overscan: 5,
  });

  if ((!prunedItems || prunedItems.length === 0) && !hasNestedGroups) {
    return null;
  }

  return (
    <DropdownMenuSub
      key={index}
      onOpenChange={(open) => {
        if (open) {
          virtualizer.measure();
        }
      }}
    >
      <DropdownMenuSubTrigger
        className="flex-1"
        ref={
          isFirstGroup && (!hasPrecedingItems || !!search)
            ? firstSubMenuRef
            : undefined
        }
        onKeyDown={(e) => {
          if (
            isFirstGroup &&
            (!hasPrecedingItems || !!search) &&
            e.key === "ArrowUp"
          ) {
            e.preventDefault();
            inputRef.current?.focus();
          }
        }}
      >
        {renderGroupLabel ? renderGroupLabel(groupLabel) : groupLabel}
      </DropdownMenuSubTrigger>
      <DropdownMenuSubContent>
        {virtualizedItems.length > 0 && (
          <div
            ref={scrollContainerRef}
            className="flex max-h-[315px] w-80 flex-1 flex-col overflow-y-auto overflow-x-hidden"
          >
            <div
              className="relative w-80"
              style={{
                height: virtualizer.getTotalSize(),
                minHeight: virtualizer.getTotalSize(),
              }}
            >
              {renderItems
                ? renderItems(
                    prunedItems,
                    DropdownItemComponent,
                    !!debouncedSearch,
                  )
                : virtualizer.getVirtualItems().map((virtualItem) => {
                    const item = virtualizedItems[virtualItem.index];
                    return (
                      <div
                        key={virtualItem.key}
                        className="absolute left-0 top-0 h-fit w-full empty:hidden"
                        style={{
                          minHeight: `${virtualItem.size}px`,
                          transform: `translateY(${virtualItem.start}px)`,
                        }}
                        data-index={virtualItem.index}
                        ref={virtualizer.measureElement}
                      >
                        <DropdownItemComponent item={item} />
                      </div>
                    );
                  })}
            </div>
          </div>
        )}
        {hasNestedGroups && (
          <>
            {virtualizedItems.length > 0 && <DropdownMenuSeparator />}
            <DropdownMenuGroup>
              {nestedSubGroups?.map((nestedSubGroup, j) => (
                <SubGroupRenderer
                  key={j}
                  subGroup={nestedSubGroup}
                  index={j}
                  isFirstGroup={j === 0}
                  hasPrecedingItems={false}
                  search={search}
                  debouncedSearch={debouncedSearch}
                  firstSubMenuRef={firstSubMenuRef}
                  lastItemRef={lastItemRef}
                  inputRef={inputRef}
                  renderGroupLabel={renderGroupLabel}
                  renderItems={renderItems}
                  DropdownItemComponent={DropdownItemComponent}
                  pruneItems={pruneItems}
                />
              ))}
            </DropdownMenuGroup>
          </>
        )}
      </DropdownMenuSubContent>
    </DropdownMenuSub>
  );
};

interface NestedDropdownProps<T> {
  open?: boolean;
  setOpen?: (open: boolean) => void;
  isInSubMenu?: boolean;
  objectType: string;
  dropdownItems?: {
    groupLabel: string;
    items: T[];
    hideGroupLabel?: boolean;
  };
  renderItems?: (
    items: T[],
    DropdownItemComponent: ForwardRefExoticComponent<
      { item: T } & RefAttributes<HTMLDivElement>
    >,
    isSearching?: boolean,
  ) => React.ReactNode;
  subGroups?: NestedSubGroup<T>[];
  renderGroupLabel?: (label: string, nested?: boolean) => React.ReactNode;
  DropdownItemComponent: ForwardRefExoticComponent<
    { item: T } & RefAttributes<HTMLDivElement> & HTMLAttributes<HTMLDivElement>
  >;
  selectedItems?: T | T[];
  filterItems: (search: string, items: T[]) => T[];
  additionalActions?: AdditionalAction[];
  align?: DropdownMenuContentProps["align"];
  hideSelected?: boolean;
  modal?: boolean;
  emptyMessage?: React.ReactNode;
  className?: string;
  placeholder?: string;
}

export const NestedDropdown = <T,>({
  open,
  setOpen,
  isInSubMenu,
  objectType,
  dropdownItems,
  renderItems,
  subGroups,
  additionalActions,
  filterItems,
  align = "start",
  renderGroupLabel,
  DropdownItemComponent,
  selectedItems,
  hideSelected,
  modal,
  children,
  emptyMessage,
  className,
  placeholder,
}: PropsWithChildren<NestedDropdownProps<T>>) => {
  const [search, setSearch] = useState("");

  // Debounce search input to reduce unnecessary filtering on large datasets
  const debouncedSearch = useDebounce(search, 150);

  const isSubGroupsEmpty = (groups: NestedSubGroup<T>[]): boolean => {
    return groups.every(({ items, subGroups: nestedSubGroups }) => {
      const hasItems = items && items.length > 0;
      const hasNestedGroups =
        nestedSubGroups &&
        nestedSubGroups.length > 0 &&
        !isSubGroupsEmpty(nestedSubGroups);
      return !hasItems && !hasNestedGroups;
    });
  };

  // Flatten data for filtering once rather than on every search
  const flattenedData = useMemo(() => {
    const flattenSubGroups = (
      groups: NestedSubGroup<T>[],
    ): Array<{
      groupLabel: string;
      items: T[];
    }> => {
      const result: Array<{ groupLabel: string; items: T[] }> = [];

      const traverse = (groups: NestedSubGroup<T>[]) => {
        for (const {
          groupLabel,
          items,
          subGroups: nestedSubGroups,
        } of groups) {
          if (items && items.length > 0) {
            result.push({ groupLabel, items });
          }
          if (nestedSubGroups && nestedSubGroups.length > 0) {
            traverse(nestedSubGroups);
          }
        }
      };

      traverse(groups);
      return result;
    };

    const combined = dropdownItems ? [dropdownItems] : [];
    const flattenedSubGroups = flattenSubGroups(subGroups || []);

    return [...combined, ...flattenedSubGroups];
  }, [dropdownItems, subGroups]);

  const filtered = useMemo(() => {
    if (!debouncedSearch.length) return undefined;

    const results: [string, T[]][] = [];

    for (const { groupLabel, items } of flattenedData) {
      if (!items?.length) continue;

      const filteredItems = filterItems(debouncedSearch, items);
      if (filteredItems && filteredItems.length) {
        results.push([groupLabel, filteredItems]);
      }
    }

    return results;
  }, [flattenedData, debouncedSearch, filterItems]);

  const inputRef = useRef<HTMLInputElement>(null);
  const firstItemRef = useRef<HTMLDivElement>(null);
  const lastItemRef = useRef<HTMLDivElement>(null);
  const firstSubMenuRef = useRef<HTMLDivElement>(null);
  const filteredListRef = useRef<HTMLDivElement>(null);
  const itemListRef = useRef<HTMLDivElement>(null);

  const selectedItemsArray = useMemo(() => {
    return selectedItems
      ? Array.isArray(selectedItems)
        ? selectedItems
        : [selectedItems]
      : [];
  }, [selectedItems]);

  const pruneItems = useCallback(
    (items: T[]) => {
      if (debouncedSearch || !selectedItemsArray.length) {
        return items;
      }
      return items.filter((item) => !selectedItemsArray.includes(item));
    },
    [selectedItemsArray, debouncedSearch],
  );

  const flattenedFilteredItems = useMemo(() => {
    if (!filtered) return [];
    return filtered.flatMap(([group, items]) => [
      { type: "group" as const, group, items: [] },
      ...items.map((item) => ({ type: "item" as const, group, item })),
    ]);
  }, [filtered]);

  const filteredVirtualizer = useVirtualizer({
    count: flattenedFilteredItems.length,
    getScrollElement: () => filteredListRef.current,
    estimateSize: () => 28,
    overscan: 5,
  });

  const itemCount = dropdownItems?.items.length ?? 0;
  const itemVirtualizer = useVirtualizer({
    count: itemCount,
    getScrollElement: () => itemListRef.current,
    estimateSize: () => 28,
    overscan: 5,
  });

  // This is a workaround to ensure the virtualizer measures the items when the dropdown is opened
  // For top-level dropdowns, and subgroups, we do this in the onOpenChange handler. When NestedDropdown is used in a submenu,
  // we don't have access to onOpenChange, so we need to use a useLayoutEffect to measure the items when the dropdown is opened.
  const animationFrameRef = useRef<number | null>(null);
  useLayoutEffect(() => {
    // Wait for the element to actually have dimensions
    const checkAndMeasure = () => {
      if (itemListRef.current && itemListRef.current.offsetHeight > 0) {
        itemVirtualizer.measure();
      } else {
        animationFrameRef.current = requestAnimationFrame(checkAndMeasure);
      }
    };

    if (open && isInSubMenu && itemCount > 0) {
      checkAndMeasure();
    }

    return () => {
      if (animationFrameRef.current != null) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    };
  }, [itemVirtualizer, open, isInSubMenu, itemCount]);

  const contents = (
    <>
      <div className="flex flex-none items-center px-2">
        <Search className="size-3 shrink-0 opacity-50" />
        <Input
          ref={inputRef}
          type="text"
          placeholder={placeholder ?? `Find a ${objectType.toLowerCase()}`}
          tabIndex={0}
          onKeyDown={(e) => {
            e.stopPropagation();
            if (e.key === "ArrowDown") {
              e.preventDefault();
              if (firstItemRef.current) {
                firstItemRef.current.focus();
              } else if (firstSubMenuRef.current) {
                firstSubMenuRef.current.focus();
              }
            }
          }}
          autoFocus
          className="h-7 border-0 !bg-transparent px-1.5 text-xs outline-none ring-0 focus-visible:border-0 focus-visible:ring-0"
          value={search}
          onChange={useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
            e.stopPropagation();
            e.preventDefault();
            setSearch(e.target.value);
          }, [])}
        />
      </div>
      {!filtered &&
      (!dropdownItems || dropdownItems?.items.length === 0) &&
      (!subGroups || subGroups?.length === 0 || isSubGroupsEmpty(subGroups)) ? (
        <>
          <DropdownMenuSeparator />
          <span className="flex w-full flex-1 items-center px-2 py-1.5 text-xs text-primary-500">
            {emptyMessage || `No ${objectType}s found`}
          </span>
        </>
      ) : (
        <>
          {!hideSelected &&
            selectedItemsArray.length > 0 &&
            !debouncedSearch && (
              <>
                <DropdownMenuSeparator />
                <DropdownMenuGroup className="-m-1 flex-1 overflow-auto p-1">
                  {selectedItemsArray.map((item, i) => (
                    <DropdownItemComponent
                      onKeyDown={(e) => {
                        if (i === 0 && e.key === "ArrowUp") {
                          e.preventDefault();
                          inputRef.current?.focus();
                        }
                      }}
                      item={item}
                      key={i}
                      ref={i === 0 ? firstItemRef : undefined}
                    />
                  ))}
                </DropdownMenuGroup>
              </>
            )}
          {filtered ? (
            flattenedFilteredItems.length ? (
              <>
                <DropdownMenuSeparator />
                <div
                  className="-m-1 max-h-full flex-1 overflow-y-auto overflow-x-hidden p-1"
                  ref={filteredListRef}
                >
                  <div
                    className="relative"
                    style={{
                      height: `${filteredVirtualizer.getTotalSize()}px`,
                    }}
                  >
                    {filteredVirtualizer
                      .getVirtualItems()
                      .map((virtualItem) => {
                        const item = flattenedFilteredItems[virtualItem.index];
                        const isFirstInteractiveItem =
                          virtualItem.index === 0 ||
                          (virtualItem.index === 1 &&
                            flattenedFilteredItems[0].type === "group" &&
                            (!selectedItems || !!debouncedSearch));
                        return (
                          <div
                            key={virtualItem.key}
                            className="absolute left-0 top-0 h-fit w-full overflow-x-hidden"
                            style={{
                              minHeight: `${virtualItem.size}px`,
                              transform: `translateY(${virtualItem.start}px)`,
                            }}
                            data-index={virtualItem.index}
                            ref={filteredVirtualizer.measureElement}
                          >
                            {item.type === "group" ? (
                              <DropdownMenuLabel className="pl-2 text-xs font-normal text-primary-500">
                                {renderGroupLabel
                                  ? renderGroupLabel(item.group, true)
                                  : item.group}
                              </DropdownMenuLabel>
                            ) : (
                              <DropdownItemComponent
                                item={item.item}
                                ref={
                                  isFirstInteractiveItem
                                    ? firstItemRef
                                    : undefined
                                }
                              />
                            )}
                          </div>
                        );
                      })}
                  </div>
                </div>
              </>
            ) : (
              <>
                <DropdownMenuSeparator />
                <span className="flex w-full flex-1 items-center px-2 py-1.5 text-xs text-primary-500">
                  No {objectType}s found
                </span>
              </>
            )
          ) : (
            <>
              {dropdownItems && (
                <>
                  <DropdownMenuSeparator />
                  <DropdownMenuGroup className="relative flex flex-col overflow-auto">
                    {!dropdownItems.hideGroupLabel && (
                      <DropdownMenuLabel>
                        {dropdownItems.groupLabel}
                      </DropdownMenuLabel>
                    )}
                    {renderItems ? (
                      renderItems(
                        dropdownItems.items,
                        DropdownItemComponent,
                        !!debouncedSearch,
                      )
                    ) : (
                      <div
                        className="-m-1 max-h-full flex-1 overflow-y-auto overflow-x-hidden p-1"
                        ref={itemListRef}
                      >
                        <div
                          className="relative"
                          style={{
                            height: `${itemVirtualizer.getTotalSize()}px`,
                            minHeight: `${itemVirtualizer.getTotalSize()}px`,
                          }}
                        >
                          {itemVirtualizer
                            .getVirtualItems()
                            .map((virtualItem) => {
                              const item =
                                dropdownItems.items[virtualItem.index];
                              const isFirstItem = virtualItem.index === 0;
                              const isLastItem =
                                virtualItem.index ===
                                dropdownItems.items.length - 1;
                              return (
                                <div
                                  key={virtualItem.key}
                                  className="absolute left-0 top-0 h-fit w-full overflow-x-hidden"
                                  style={{
                                    minHeight: `${virtualItem.size}px`,
                                    transform: `translateY(${virtualItem.start}px)`,
                                  }}
                                  data-index={virtualItem.index}
                                  ref={itemVirtualizer.measureElement}
                                >
                                  <DropdownItemComponent
                                    item={item}
                                    ref={
                                      !selectedItems || !debouncedSearch
                                        ? isFirstItem
                                          ? firstItemRef
                                          : isLastItem
                                            ? lastItemRef
                                            : undefined
                                        : undefined
                                    }
                                  />
                                </div>
                              );
                            })}
                        </div>
                      </div>
                    )}
                  </DropdownMenuGroup>
                </>
              )}
              {subGroups && subGroups?.length > 0 && <DropdownMenuSeparator />}
              {/* min-h-fit does not apply for some reason. minheight here is a workaround to ensure subgroup doesn't get cut off */}
              <DropdownMenuGroup
                className="overflow-auto"
                style={{ minHeight: "fit-content" }}
              >
                {subGroups?.map((subGroup, i) => (
                  <SubGroupRenderer
                    key={i}
                    subGroup={subGroup}
                    index={i}
                    isFirstGroup={i === 0}
                    hasPrecedingItems={
                      itemCount > 0 || selectedItemsArray.length > 0
                    }
                    search={search}
                    debouncedSearch={debouncedSearch}
                    firstSubMenuRef={firstSubMenuRef}
                    lastItemRef={lastItemRef}
                    inputRef={inputRef}
                    renderGroupLabel={renderGroupLabel}
                    renderItems={renderItems}
                    DropdownItemComponent={DropdownItemComponent}
                    pruneItems={pruneItems}
                  />
                ))}
              </DropdownMenuGroup>
            </>
          )}
        </>
      )}
      {!!additionalActions && additionalActions.length > 0 && (
        <>
          <DropdownMenuSeparator />
          {additionalActions.map(
            ({ label, onSelect, className, disabled }, i) => (
              <DropdownMenuItem
                asChild
                onSelect={onSelect}
                key={i}
                className={className}
                disabled={disabled}
              >
                {label}
              </DropdownMenuItem>
            ),
          )}
        </>
      )}
    </>
  );

  const onKeyDown = (e: React.KeyboardEvent<HTMLDivElement>) => {
    e.stopPropagation();
    if (
      e.target === firstItemRef.current &&
      (!selectedItems || !debouncedSearch) &&
      e.key === "ArrowUp"
    ) {
      e.preventDefault();
      inputRef.current?.focus();
    }
  };

  if (isInSubMenu) {
    return (
      <DropdownMenuSubContent
        onKeyDown={onKeyDown}
        className="flex w-80 flex-col overflow-auto"
      >
        {contents}
      </DropdownMenuSubContent>
    );
  }

  return (
    <DropdownMenu
      open={open}
      onOpenChange={(open) => {
        !open && setSearch("");
        setOpen?.(open);
        if (open && itemCount > 0) {
          // Wait for the element to actually have dimensions
          const checkAndMeasure = () => {
            if (itemListRef.current && itemListRef.current.offsetHeight > 0) {
              itemVirtualizer.measure();
            } else {
              requestAnimationFrame(checkAndMeasure);
            }
          };
          checkAndMeasure();
        }
      }}
      modal={modal}
    >
      <DropdownMenuTrigger asChild>{children}</DropdownMenuTrigger>
      <DropdownMenuContent
        align={align}
        className={cn("flex min-w-80 flex-col overflow-hidden", className)}
        id={`nested-dropdown-${objectType}`}
        onKeyDown={onKeyDown}
      >
        {contents}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};
