import { But<PERSON> } from "#/ui/button";
import { type ChatSessions, type ChatSession } from "./use-global-chat-context";
import { OctagonAlert, History, Trash } from "lucide-react";
import { cn } from "#/utils/classnames";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "#/ui/dropdown-menu";
import { smartTimeFormat } from "#/ui/date";
import { BasicTooltip } from "#/ui/tooltip";

export const SessionsHistoryDropdown = ({
  isDocked,
  chatSessions,
  setCurrentChatSessionId,
  deleteSession,
  currentChatSessionId,
  isConfirming,
  className,
  size = "widget",
}: {
  isDocked: boolean;
  chatSessions: ChatSessions;
  setCurrentChatSessionId: (id: string) => void;
  deleteSession: (sessionId: string) => void;
  currentChatSessionId: string;
  isConfirming: boolean;
  className?: string;
  size?: "widget" | "full";
}) => {
  const sessions: ChatSession[] = chatSessions.sessions.toReversed();

  return (
    <DropdownMenu>
      <BasicTooltip tooltipContent="Chat history">
        <DropdownMenuTrigger asChild>
          <Button
            variant={"ghost"}
            size="icon"
            Icon={History}
            iconClassName={size === "full" ? "size-4" : undefined}
            className={cn(
              "size-7 pointer-events-auto bg-primary-50 hover:bg-primary-200",
              size === "full" && "size-8",
              className,
            )}
          />
        </DropdownMenuTrigger>
      </BasicTooltip>
      <DropdownMenuContent className="w-[280px]" align="start">
        {sessions.length > 0 &&
          sessions.map((session) => {
            const message =
              session.messages.length > 0
                ? session.messages[0].type === "user_message"
                  ? session.messages[0].message
                  : session.messages[0].type === "llm_message"
                    ? session.messages[0].llmContent
                    : session.messages[0].type === "tool_interaction"
                      ? session.messages[0].functionName
                      : "New chat"
                : "New chat";
            const createdAt = new Date(session.createdAt);
            return (
              <DropdownMenuItem
                key={`session-item-${session.id}`}
                onClick={() => {
                  setCurrentChatSessionId(session.id);
                }}
                className={cn(
                  "flex justify-between w-full",
                  currentChatSessionId === session.id && "bg-primary-100",
                )}
              >
                {session.isActive && isConfirming && (
                  <OctagonAlert className="size-3 text-accent-500" />
                )}
                <span className="flex-1 truncate text-xs">{message}</span>
                <span className="max-w-[68px] flex-none truncate text-xs text-primary-500">
                  {currentChatSessionId === session.id
                    ? "Current"
                    : smartTimeFormat(createdAt.getTime())}
                </span>
                {sessions.length > 1 && (
                  <Button
                    variant="ghost"
                    size="icon"
                    Icon={Trash}
                    className="size-5 text-primary-500"
                    onClick={(e) => {
                      e.stopPropagation();
                      deleteSession(session.id);
                    }}
                  />
                )}
              </DropdownMenuItem>
            );
          })}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};
