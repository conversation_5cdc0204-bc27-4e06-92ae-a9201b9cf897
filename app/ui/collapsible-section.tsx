import { type PropsWithChildren, useEffect, useState } from "react";
import { buttonVariants } from "#/ui/button";
import { ChevronDown, type LucideIcon } from "lucide-react";
import { cn } from "#/utils/classnames";
import { useEntityStorage } from "#/lib/clientDataStorage";

export type CollapsibleSectionProps = PropsWithChildren<{
  title: React.ReactNode;
  id?: string;
  defaultCollapsed?: boolean;
  className?: string;
  actionRight?: React.ReactNode;
  localStorageKey?: string;
  onChange?: (isCollapsed: boolean) => void;
  forcedState?: "collapsed" | "expanded" | "mixed";
  keepContentsMounted?: boolean;
  expandedClassName?: string;
  collapsedClassName?: string;
  draggableId?: string;
  onToggleLayout?: (draggableId: string) => void;
  Icon?: LucideIcon;
  layout?: "full" | "two_column" | null;
}>;

export const CollapsibleSection = (props: CollapsibleSectionProps) => {
  if (props.localStorageKey) {
    return (
      <WithLocalStorage {...props} localStorageKey={props.localStorageKey} />
    );
  }
  return <WithoutLocalStorage {...props} />;
};

const WithLocalStorage = (
  props: CollapsibleSectionProps & { localStorageKey: string },
) => {
  const [isCollapsed, setCollapsed] = useEntityStorage({
    entityType: "collapsibleSection",
    entityIdentifier: props.localStorageKey,
    key: "isCollapsed",
    defaultValue: props.defaultCollapsed,
    persistDefaultValueOnMiss: false,
  });
  return (
    <CollapsibleSectionContent
      {...props}
      isCollapsed={isCollapsed}
      setCollapsed={setCollapsed}
    />
  );
};

const WithoutLocalStorage = (props: CollapsibleSectionProps) => {
  const [isCollapsed, setCollapsed] = useState(props.defaultCollapsed ?? false);

  return (
    <CollapsibleSectionContent
      {...props}
      isCollapsed={isCollapsed}
      setCollapsed={setCollapsed}
    />
  );
};

const CollapsibleSectionContent = ({
  title,
  id,
  children,
  className,
  actionRight,
  isCollapsed,
  setCollapsed,
  forcedState,
  keepContentsMounted,
  onChange,
  expandedClassName = "",
  collapsedClassName = "",
  Icon,
}: CollapsibleSectionProps & {
  isCollapsed: boolean;
  setCollapsed: (value: boolean) => void;
}) => {
  useEffect(() => {
    if (forcedState === "collapsed") {
      setCollapsed(true);
    } else if (forcedState === "expanded") {
      setCollapsed(false);
    }
  }, [forcedState, setCollapsed]);

  const contents = keepContentsMounted ? (
    <div className={cn({ hidden: isCollapsed })}>{children}</div>
  ) : isCollapsed ? null : (
    children
  );

  return (
    <>
      <div className="flex w-full gap-2">
        <div
          data-scroll-id={id}
          className={cn(
            buttonVariants({ variant: "ghost", size: "xs" }),
            "flex-1 flex items-center gap-2 text-left font-inter text-xs font-medium px-1.5 transition-all cursor-pointer",
            {
              "p-0 hover:px-1.5 mb-1": !isCollapsed,
              "bg-primary-100 hover:bg-primary-200/60": isCollapsed,
            },
            className,
            {
              [expandedClassName]: !isCollapsed,
              [collapsedClassName]: isCollapsed,
            },
          )}
          onClick={(e) => {
            e.preventDefault();
            const newState = !isCollapsed;
            setCollapsed(newState);
            onChange?.(newState);
          }}
          title={`Click to ${isCollapsed ? "expand" : "collapse"}`}
        >
          <span className="flex flex-1 items-center truncate">
            {Icon && (
              <Icon className="mr-1.5 inline-block size-3 text-primary-400" />
            )}
            {title}
          </span>
          <ChevronDown
            className={cn(
              "size-3 flex-none text-primary-500 transition-transform",
              {
                "-rotate-90": isCollapsed,
              },
            )}
          />
        </div>
        {actionRight}
      </div>
      {contents}
    </>
  );
};

export const ControlledCollapsibleSection = (
  props: Omit<CollapsibleSectionProps, "localStorageKey"> & {
    isCollapsed: boolean;
    setIsCollapsed: (isCollapsed: boolean) => void;
  },
) => {
  return (
    <CollapsibleSectionContent
      {...props}
      isCollapsed={props.isCollapsed}
      setCollapsed={props.setIsCollapsed}
    />
  );
};
